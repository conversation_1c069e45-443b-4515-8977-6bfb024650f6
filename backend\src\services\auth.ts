import jwt from 'jsonwebtoken'
import * as bcrypt from 'bcryptjs'
import { prisma } from '@/config/database'
import { getEnv } from '@/config/env'

// UserRole enum - matches Prisma schema
export enum UserRole {
  ADMIN = 'ADMIN',
  DEVELOPER = 'DEVELOPER',
  USER = 'USER'
}

export interface JWTPayload {
  userId: string
  email: string
  role: UserRole
  tenantId?: string
}

export interface AuthResult {
  user: {
    id: string
    email: string
    name: string | null
    role: UserRole
    tenantId: string | null
  }
  token: string
  refreshToken: string
}

export class AuthService {
  private static readonly SALT_ROUNDS = 12
  private static readonly REFRESH_TOKEN_EXPIRY = '30d'

  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.SALT_ROUNDS)
  }

  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash)
  }

  static generateToken(payload: JWTPayload): string {
    const env = getEnv()
    return jwt.sign(payload, env.JWT_SECRET, {
      expiresIn: env.JWT_EXPIRES_IN,
      issuer: 'synapseai',
      audience: 'synapseai-users'
    })
  }

  static generateRefreshToken(userId: string): string {
    const env = getEnv()
    return jwt.sign({ userId, type: 'refresh' }, env.JWT_SECRET, {
      expiresIn: this.REFRESH_TOKEN_EXPIRY,
      issuer: 'synapseai',
      audience: 'synapseai-refresh'
    })
  }

  static verifyToken(token: string): JWTPayload {
    const env = getEnv()
    return jwt.verify(token, env.JWT_SECRET, {
      issuer: 'synapseai',
      audience: 'synapseai-users'
    }) as JWTPayload
  }

  static verifyRefreshToken(token: string): { userId: string } {
    const env = getEnv()
    return jwt.verify(token, env.JWT_SECRET, {
      issuer: 'synapseai',
      audience: 'synapseai-refresh'
    }) as { userId: string }
  }

  static async register(email: string, password: string, name?: string, tenantId?: string): Promise<AuthResult> {
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      throw new Error('User already exists')
    }

    const hashedPassword = await this.hashPassword(password)

    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name,
        tenantId,
        role: tenantId ? UserRole.USER : UserRole.ADMIN
      }
    })

    const payload: JWTPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      tenantId: user.tenantId
    }

    const token = this.generateToken(payload)
    const refreshToken = this.generateRefreshToken(user.id)

    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        tenantId: user.tenantId
      },
      token,
      refreshToken
    }
  }

  static async login(email: string, password: string): Promise<AuthResult> {
    const user = await prisma.user.findUnique({
      where: { email }
    })

    if (!user) {
      throw new Error('Invalid credentials')
    }

    const isValidPassword = await this.verifyPassword(password, user.password)
    if (!isValidPassword) {
      throw new Error('Invalid credentials')
    }

    const payload: JWTPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      tenantId: user.tenantId
    }

    const token = this.generateToken(payload)
    const refreshToken = this.generateRefreshToken(user.id)

    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        tenantId: user.tenantId
      },
      token,
      refreshToken
    }
  }

  static async refreshToken(refreshToken: string): Promise<{ token: string; refreshToken: string }> {
    const { userId } = this.verifyRefreshToken(refreshToken)

    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      throw new Error('User not found')
    }

    const payload: JWTPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      tenantId: user.tenantId
    }

    const newToken = this.generateToken(payload)
    const newRefreshToken = this.generateRefreshToken(user.id)

    return {
      token: newToken,
      refreshToken: newRefreshToken
    }
  }

  static async getUserById(userId: string) {
    return prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        tenantId: true,
        createdAt: true,
        updatedAt: true
      }
    })
  }
}

export class RBACService {
  private static readonly PERMISSIONS = {
    [UserRole.ADMIN]: [
      'users:read', 'users:write', 'users:delete',
      'agents:read', 'agents:write', 'agents:delete',
      'tools:read', 'tools:write', 'tools:delete',
      'providers:read', 'providers:write', 'providers:delete',
      'sessions:read', 'sessions:write', 'sessions:delete',
      'system:admin'
    ],
    [UserRole.DEVELOPER]: [
      'agents:read', 'agents:write',
      'tools:read', 'tools:write',
      'providers:read',
      'sessions:read', 'sessions:write'
    ],
    [UserRole.USER]: [
      'agents:read',
      'sessions:read', 'sessions:write'
    ]
  }

  static hasPermission(userRole: UserRole, permission: string): boolean {
    return this.PERMISSIONS[userRole]?.includes(permission) || false
  }

  static canAccessTenant(userTenantId: string | null, resourceTenantId: string | null): boolean {
    if (!userTenantId) return true // Admin users
    return userTenantId === resourceTenantId
  }

  static filterByTenant<T extends { tenantId: string | null }>(
    resources: T[],
    userTenantId: string | null
  ): T[] {
    if (!userTenantId) return resources // Admin users see all
    return resources.filter(resource => resource.tenantId === userTenantId)
  }
}
