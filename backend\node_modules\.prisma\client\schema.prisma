// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String?
  role      UserRole @default(USER)
  tenantId  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant   Tenant?   @relation(fields: [tenantId], references: [id])
  agents   Agent[]
  sessions Session[]
  apiKeys  Api<PERSON>ey[]

  @@map("users")
}

enum UserRole {
  ADMIN
  DEVELOPER
  USER
}

// Tenant Management
model Tenant {
  id        String   @id @default(cuid())
  name      String
  domain    String?  @unique
  settings  Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  users     User[]
  agents    Agent[]
  providers Provider[]

  @@map("tenants")
}

// AI Providers
model Provider {
  id        String       @id @default(cuid())
  name      String
  type      ProviderType
  config    Json
  isActive  Boolean      @default(true)
  tenantId  String?
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt

  // Relations
  tenant   Tenant?   @relation(fields: [tenantId], references: [id])
  sessions Session[]

  @@map("providers")
}

enum ProviderType {
  OPENAI
  ANTHROPIC
  GOOGLE
  CUSTOM
}

// Tools
model Tool {
  id          String   @id @default(cuid())
  name        String
  description String?
  type        ToolType
  schema      Json
  config      Json
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  agents AgentTool[]

  @@map("tools")
}

enum ToolType {
  INTERNAL
  EXTERNAL
  API
  WEBHOOK
}

// Agents
model Agent {
  id          String      @id @default(cuid())
  name        String
  description String?
  config      Json
  status      AgentStatus @default(INACTIVE)
  userId      String
  tenantId    String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  user     User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  tenant   Tenant?     @relation(fields: [tenantId], references: [id])
  tools    AgentTool[]
  sessions Session[]

  @@map("agents")
}

enum AgentStatus {
  ACTIVE
  INACTIVE
  TRAINING
  ERROR
}

// Agent-Tool Junction Table
model AgentTool {
  id       String @id @default(cuid())
  agentId  String
  toolId   String
  config   Json?
  priority Int    @default(0)

  // Relations
  agent Agent @relation(fields: [agentId], references: [id], onDelete: Cascade)
  tool  Tool  @relation(fields: [toolId], references: [id], onDelete: Cascade)

  @@unique([agentId, toolId])
  @@map("agent_tools")
}

// Sessions
model Session {
  id         String        @id @default(cuid())
  userId     String
  agentId    String?
  providerId String?
  context    Json?
  metadata   Json?
  status     SessionStatus @default(ACTIVE)
  expiresAt  DateTime?
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt

  // Relations
  user     User      @relation(fields: [userId], references: [id])
  agent    Agent?    @relation(fields: [agentId], references: [id])
  provider Provider? @relation(fields: [providerId], references: [id])
  events   Event[]

  @@map("sessions")
}

enum SessionStatus {
  ACTIVE
  INACTIVE
  EXPIRED
  TERMINATED
}

// Events (UAUI Protocol)
model Event {
  id        String    @id @default(cuid())
  sessionId String
  type      EventType
  data      Json
  timestamp DateTime  @default(now())

  // Relations
  session Session @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@index([sessionId])
  @@index([type])
  @@index([timestamp])
  @@map("events")
}

// API Keys and Tokens
model ApiKey {
  id        String    @id @default(cuid())
  name      String
  key       String    @unique
  userId    String
  scopes    String[]
  expiresAt DateTime?
  lastUsed  DateTime?
  createdAt DateTime  @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

// Audit Logs
model AuditLog {
  id         String   @id @default(cuid())
  userId     String?
  action     String
  resource   String
  resourceId String?
  metadata   Json?
  ipAddress  String?
  userAgent  String?
  timestamp  DateTime @default(now())

  @@index([userId])
  @@index([action])
  @@index([resource])
  @@index([timestamp])
  @@map("audit_logs")
}

// Usage Analytics
model UsageMetric {
  id         String   @id @default(cuid())
  userId     String?
  tenantId   String?
  metricType String
  value      Float
  metadata   Json?
  timestamp  DateTime @default(now())

  @@index([userId])
  @@index([tenantId])
  @@index([metricType])
  @@index([timestamp])
  @@map("usage_metrics")
}

// Webhooks
model Webhook {
  id            String    @id @default(cuid())
  url           String
  events        String[]
  secret        String?
  isActive      Boolean   @default(true)
  userId        String
  tenantId      String?
  lastTriggered DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("webhooks")
}

// File Uploads
model FileUpload {
  id           String   @id @default(cuid())
  filename     String
  originalName String
  mimeType     String
  size         Int
  path         String
  userId       String
  metadata     Json?
  createdAt    DateTime @default(now())

  @@map("file_uploads")
}

enum EventType {
  AGENT_START
  AGENT_STOP
  TOOL_CALL
  TOOL_RESPONSE
  PROVIDER_REQUEST
  PROVIDER_RESPONSE
  USER_MESSAGE
  AGENT_MESSAGE
  ERROR
  SYSTEM
}
